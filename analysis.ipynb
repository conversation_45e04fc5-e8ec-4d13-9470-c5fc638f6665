import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, RepeatVector, TimeDistributed, Dropout, BatchNormalization
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.optimizers import Adam
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
import matplotlib.pyplot as plt
import seaborn as sns
import time
import warnings
warnings.filterwarnings('ignore')

# Configure matplotlib for better plots
plt.style.use('seaborn-v0_8')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

# Configure TensorFlow for optimal performance
print(f"TensorFlow version: {tf.__version__}")
print(f"GPU Available: {tf.config.list_physical_devices('GPU')}")

# Enable memory growth for GPU if available
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"GPU memory growth enabled for {len(gpus)} GPU(s)")
    except RuntimeError as e:
        print(f"GPU configuration error: {e}")

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

print("\n=== Optimized TTC Ridership Analysis ===")
start_time = time.time()

# Generate synthetic TTC ridership data with optimized operations
print("\n1. Generating synthetic data...")
date_rng = pd.date_range(start='2022-01-01', end='2023-12-31 23:00', freq='h')
n_samples = len(date_rng)

# Vectorized datetime feature extraction
hours = np.array([dt.hour for dt in date_rng])
dow = np.array([dt.dayofweek for dt in date_rng])

# Vectorized ridership simulation with improved seasonality
ridership = (
    1000
    + 200 * np.sin(2 * np.pi * hours / 24)  # Daily pattern
    + 300 * np.sin(2 * np.pi * dow / 7)     # Weekly pattern
    + 100 * np.sin(2 * np.pi * hours / 12)  # Semi-daily pattern
    + 50 * np.random.randn(n_samples)       # Noise
)

# Create DataFrame efficiently
df = pd.DataFrame({
    'timestamp': date_rng,
    'hour': hours,
    'dow': dow,
    'ridership': ridership
})

print(f"Generated {n_samples:,} data points")

# Optimized data preprocessing
print("\n2. Preprocessing data...")
preprocessing_start = time.time()

# Normalize ridership data
scaler = MinMaxScaler(feature_range=(0, 1))
ridership_norm = scaler.fit_transform(df[['ridership']]).flatten()

print(f"Data normalized in {time.time() - preprocessing_start:.3f}s")

# Ultra-fast sequence creation using numpy stride tricks
def create_sequences_optimized(data, n_past=24, n_future=1):
    """
    Ultra-optimized sequence creation using numpy operations.
    """
    n_samples = len(data) - n_past - n_future + 1
    
    # Use numpy's advanced indexing for ultra-fast sequence creation
    indices = np.arange(n_samples)[:, None] + np.arange(n_past + n_future)
    sequences = data[indices]
    
    X = sequences[:, :n_past].reshape(-1, n_past, 1).astype(np.float32)
    y = sequences[:, n_past:].astype(np.float32)
    
    return X, y

print("\n3. Creating sequences...")
sequence_start = time.time()

X, y = create_sequences_optimized(ridership_norm, n_past=24, n_future=1)

print(f"Created {X.shape[0]:,} sequences in {time.time() - sequence_start:.3f}s")
print(f"X shape: {X.shape}, y shape: {y.shape}")
print(f"Memory usage: X={X.nbytes / 1024**2:.1f}MB, y={y.nbytes / 1024**2:.1f}MB")

# Optimized data splitting
print("\n4. Splitting data...")
n_samples = len(X)

train_size = int(0.70 * n_samples)
val_size = int(0.15 * n_samples)

X_train, y_train = X[:train_size], y[:train_size]
X_val, y_val = X[train_size:train_size + val_size], y[train_size:train_size + val_size]
X_test, y_test = X[train_size + val_size:], y[train_size + val_size:]

print(f"Train: {X_train.shape[0]:,} samples")
print(f"Validation: {X_val.shape[0]:,} samples")
print(f"Test: {X_test.shape[0]:,} samples")

# Memory cleanup
del X, y
import gc
gc.collect()

print(f"\nData preparation completed in {time.time() - start_time:.3f}s")

# Build and train LSTM forecasting model
model = Sequential(
    [
        LSTM(64, input_shape=(X_train.shape[1], 1)),
        Dense(1),
    ]
)
model.compile(optimizer="adam", loss="mae")
history = model.fit(
    X_train, y_train, validation_data=(X_val, y_val), epochs=5, batch_size=32, verbose=0
)

# Plot loss curves
plt.figure()
plt.plot(history.history["loss"], label="train_loss")
plt.plot(history.history["val_loss"], label="val_loss")
plt.title("LSTM Training and Validation Loss")
plt.legend()
plt.show()

# Build and train autoencoder for anomaly detection
seq_len = 24
autoencoder = Sequential(
    [
        LSTM(64, activation="relu", input_shape=(seq_len, 1), return_sequences=False),
        RepeatVector(seq_len),
        LSTM(64, activation="relu", return_sequences=True),
        TimeDistributed(Dense(1)),
    ]
)
autoencoder.compile(optimizer="adam", loss="mse")
autoencoder.fit(X_train, X_train, validation_data=(X_val, X_val), epochs=5, batch_size=32, verbose=0)

# Calculate reconstruction errors on test set
reconstructions = autoencoder.predict(X_test)
mse = np.mean(np.power(X_test - reconstructions, 2), axis=(1, 2))
threshold = np.percentile(mse, 95)

# Display anomaly threshold and error distribution
print("Anomaly detection threshold (95th percentile):", threshold)

plt.figure()
plt.hist(mse, bins=50)
plt.title("Reconstruction Error Distribution (Test Set)")
plt.show()



# Show a sample prediction vs actual
sample_idx = 0
pred_norm = model.predict(X_test[sample_idx:sample_idx+1])[0][0]
actual_norm = y_test[sample_idx][0]
pred = scaler.inverse_transform([[pred_norm]])[0][0]
actual = scaler.inverse_transform([[actual_norm]])[0][0]

print(f"Sample Forecast -> Predicted: {pred:.1f} riders, Actual: {actual:.1f} riders")





