import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, RepeatVector, TimeDistributed
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt

# Generate synthetic TTC ridership data
date_rng = pd.date_range(start='2022-01-01', end='2023-12-31 23:00', freq='h')
df = pd.DataFrame(date_rng, columns=['timestamp'])
df['hour'] = df['timestamp'].dt.hour
df['dow'] = df['timestamp'].dt.dayofweek

# Simulate ridership with daily and weekly seasonality + noise
df['ridership'] = (
    1000
    + 200 * np.sin(2 * np.pi * df['hour'] / 24)
    + 300 * np.sin(2 * np.pi * df['dow'] / 7)
    + 50 * np.random.randn(len(df))
)

# Normalize ridership
scaler = MinMaxScaler()
df['ridership_norm'] = scaler.fit_transform(df[['ridership']])


# Create sequences for LSTM (past 24 hours -> next hour)
def create_sequences(data, n_past=24, n_future=1):
    Xs, ys = [], []
    for i in range(len(data) - n_past - n_future + 1):
        Xs.append(data[i : i + n_past])
        ys.append(data[i + n_past : i + n_past + n_future])
    return np.array(Xs), np.array(ys)

X, y = create_sequences(df['ridership_norm'].values, 24, 1)
X = X.reshape((X.shape[0], X.shape[1], 1))
y = y.reshape((y.shape[0], y.shape[1]))

# Split into train/val/test
train_size = int(0.7 * len(X))
val_size = int(0.15 * len(X))
X_train, y_train = X[:train_size], y[:train_size]
X_val, y_val = X[train_size : train_size + val_size], y[train_size : train_size + val_size]
X_test, y_test = X[train_size + val_size :], y[train_size + val_size :]

# Build and train LSTM forecasting model
model = Sequential(
    [
        LSTM(64, input_shape=(X_train.shape[1], 1)),
        Dense(1),
    ]
)
model.compile(optimizer="adam", loss="mae")
history = model.fit(
    X_train, y_train, validation_data=(X_val, y_val), epochs=5, batch_size=32, verbose=0
)

# Plot loss curves
plt.figure()
plt.plot(history.history["loss"], label="train_loss")
plt.plot(history.history["val_loss"], label="val_loss")
plt.title("LSTM Training and Validation Loss")
plt.legend()
plt.show()

# Build and train autoencoder for anomaly detection
seq_len = 24
autoencoder = Sequential(
    [
        LSTM(64, activation="relu", input_shape=(seq_len, 1), return_sequences=False),
        RepeatVector(seq_len),
        LSTM(64, activation="relu", return_sequences=True),
        TimeDistributed(Dense(1)),
    ]
)
autoencoder.compile(optimizer="adam", loss="mse")
autoencoder.fit(X_train, X_train, validation_data=(X_val, X_val), epochs=5, batch_size=32, verbose=0)

# Calculate reconstruction errors on test set
reconstructions = autoencoder.predict(X_test)
mse = np.mean(np.power(X_test - reconstructions, 2), axis=(1, 2))
threshold = np.percentile(mse, 95)

# Display anomaly threshold and error distribution
print("Anomaly detection threshold (95th percentile):", threshold)

plt.figure()
plt.hist(mse, bins=50)
plt.title("Reconstruction Error Distribution (Test Set)")
plt.show()



# Show a sample prediction vs actual
sample_idx = 0
pred_norm = model.predict(X_test[sample_idx:sample_idx+1])[0][0]
actual_norm = y_test[sample_idx][0]
pred = scaler.inverse_transform([[pred_norm]])[0][0]
actual = scaler.inverse_transform([[actual_norm]])[0][0]

print(f"Sample Forecast -> Predicted: {pred:.1f} riders, Actual: {actual:.1f} riders")





