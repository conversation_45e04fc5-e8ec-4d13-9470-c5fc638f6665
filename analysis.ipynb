import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, RepeatVector, TimeDistributed, Dropout, BatchNormalization
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers import Adam
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import time
import warnings
warnings.filterwarnings('ignore')

# Configure TensorFlow for optimal performance
print(f"TensorFlow version: {tf.__version__}")
print(f"GPU Available: {tf.config.list_physical_devices('GPU')}")

# Enable memory growth for GPU if available
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"GPU memory growth enabled for {len(gpus)} GPU(s)")
    except RuntimeError as e:
        print(f"GPU configuration error: {e}")

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

print("\n=== Optimized TTC Ridership Analysis ===")
start_time = time.time()

# Generate synthetic TTC ridership data with optimized operations
print("\n1. Generating synthetic data...")
date_rng = pd.date_range(start='2022-01-01', end='2023-12-31 23:00', freq='h')
n_samples = len(date_rng)

# Vectorized datetime feature extraction
hours = np.array([dt.hour for dt in date_rng])
dow = np.array([dt.dayofweek for dt in date_rng])

# Vectorized ridership simulation with improved seasonality
ridership = (
    1000
    + 200 * np.sin(2 * np.pi * hours / 24)  # Daily pattern
    + 300 * np.sin(2 * np.pi * dow / 7)     # Weekly pattern
    + 100 * np.sin(2 * np.pi * hours / 12)  # Semi-daily pattern
    + 50 * np.random.randn(n_samples)       # Noise
)

# Create DataFrame efficiently
df = pd.DataFrame({
    'timestamp': date_rng,
    'hour': hours,
    'dow': dow,
    'ridership': ridership
})

print(f"Generated {n_samples:,} data points")

# Optimized data preprocessing
print("\n2. Preprocessing data...")
preprocessing_start = time.time()

# Normalize ridership data
scaler = MinMaxScaler(feature_range=(0, 1))
ridership_norm = scaler.fit_transform(df[['ridership']]).flatten()

print(f"Data normalized in {time.time() - preprocessing_start:.3f}s")

# Optimized vectorized sequence creation
def create_sequences_vectorized(data, n_past=24, n_future=1):
    """
    Vectorized sequence creation for much better performance.
    Creates sliding windows efficiently using numpy operations.
    """
    n_samples = len(data) - n_past - n_future + 1
    
    # Pre-allocate arrays for better memory efficiency
    X = np.zeros((n_samples, n_past, 1), dtype=np.float32)
    y = np.zeros((n_samples, n_future), dtype=np.float32)
    
    # Vectorized sliding window creation
    for i in range(n_samples):
        X[i, :, 0] = data[i:i + n_past]
        y[i, :] = data[i + n_past:i + n_past + n_future]
    
    return X, y

# Alternative ultra-fast implementation using stride tricks
def create_sequences_stride(data, n_past=24, n_future=1):
    """
    Ultra-fast sequence creation using numpy stride tricks.
    Memory efficient and very fast for large datasets.
    """
    from numpy.lib.stride_tricks import sliding_window_view
    
    # Create sliding windows
    windows = sliding_window_view(data, window_shape=n_past + n_future)
    
    # Split into X and y
    X = windows[:, :n_past].reshape(-1, n_past, 1).astype(np.float32)
    y = windows[:, n_past:].astype(np.float32)
    
    return X, y

print("\n3. Creating sequences...")
sequence_start = time.time()

# Use the stride-based method for maximum performance
try:
    X, y = create_sequences_stride(ridership_norm, n_past=24, n_future=1)
    print("Using stride-based sequence creation (fastest)")
except:
    # Fallback to vectorized method
    X, y = create_sequences_vectorized(ridership_norm, n_past=24, n_future=1)
    print("Using vectorized sequence creation")

print(f"Created {X.shape[0]:,} sequences in {time.time() - sequence_start:.3f}s")
print(f"X shape: {X.shape}, y shape: {y.shape}")
print(f"Memory usage: X={X.nbytes / 1024**2:.1f}MB, y={y.nbytes / 1024**2:.1f}MB")

# Optimized data splitting with better proportions
print("\n4. Splitting data...")
n_samples = len(X)

# Use 70% train, 15% validation, 15% test
train_size = int(0.70 * n_samples)
val_size = int(0.15 * n_samples)
test_size = n_samples - train_size - val_size

# Split data efficiently
X_train = X[:train_size]
y_train = y[:train_size]
X_val = X[train_size:train_size + val_size]
y_val = y[train_size:train_size + val_size]
X_test = X[train_size + val_size:]
y_test = y[train_size + val_size:]

print(f"Train: {X_train.shape[0]:,} samples")
print(f"Validation: {X_val.shape[0]:,} samples")
print(f"Test: {X_test.shape[0]:,} samples")

# Memory cleanup
del X, y
import gc
gc.collect()

print(f"\nData preparation completed in {time.time() - start_time:.3f}s")

# Build and train LSTM forecasting model
model = Sequential(
    [
        LSTM(64, input_shape=(X_train.shape[1], 1)),
        Dense(1),
    ]
)
model.compile(optimizer="adam", loss="mae")
history = model.fit(
    X_train, y_train, validation_data=(X_val, y_val), epochs=5, batch_size=32, verbose=0
)

# Plot loss curves
plt.figure()
plt.plot(history.history["loss"], label="train_loss")
plt.plot(history.history["val_loss"], label="val_loss")
plt.title("LSTM Training and Validation Loss")
plt.legend()
plt.show()

# Build and train autoencoder for anomaly detection
seq_len = 24
autoencoder = Sequential(
    [
        LSTM(64, activation="relu", input_shape=(seq_len, 1), return_sequences=False),
        RepeatVector(seq_len),
        LSTM(64, activation="relu", return_sequences=True),
        TimeDistributed(Dense(1)),
    ]
)
autoencoder.compile(optimizer="adam", loss="mse")
autoencoder.fit(X_train, X_train, validation_data=(X_val, X_val), epochs=5, batch_size=32, verbose=0)

# Calculate reconstruction errors on test set
reconstructions = autoencoder.predict(X_test)
mse = np.mean(np.power(X_test - reconstructions, 2), axis=(1, 2))
threshold = np.percentile(mse, 95)

# Display anomaly threshold and error distribution
print("Anomaly detection threshold (95th percentile):", threshold)

plt.figure()
plt.hist(mse, bins=50)
plt.title("Reconstruction Error Distribution (Test Set)")
plt.show()



# Show a sample prediction vs actual
sample_idx = 0
pred_norm = model.predict(X_test[sample_idx:sample_idx+1])[0][0]
actual_norm = y_test[sample_idx][0]
pred = scaler.inverse_transform([[pred_norm]])[0][0]
actual = scaler.inverse_transform([[actual_norm]])[0][0]

print(f"Sample Forecast -> Predicted: {pred:.1f} riders, Actual: {actual:.1f} riders")





